import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import dotenv from 'dotenv';
import * as schema from '../lib/db/schema';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function main() {
  // Source database connection (old database)
  if (!process.env.OLD_POSTGRES_URL) {
    throw new Error('OLD_POSTGRES_URL not found in environment variables');
  }
  console.log('Connecting to source database...');
  const sourceDb = postgres(process.env.OLD_POSTGRES_URL);
  const sourceClient = drizzle(sourceDb, { schema });

  // Target database connection (new database)
  if (!process.env.POSTGRES_URL) {
    throw new Error('POSTGRES_URL not found in environment variables');
  }
  console.log('Connecting to target database...');
  const targetDb = postgres(process.env.POSTGRES_URL);
  const targetClient = drizzle(targetDb, { schema });

  try {
    // Start migration process
    console.log('Starting data migration process...');

    // Fetch data from each table in the old database
    console.log('Fetching data from source database...');
    
    console.log('Fetching users...');
    const users = await sourceClient.query.user.findMany();
    
    console.log('Fetching prompts...');
    const prompts = await sourceClient.query.prompt.findMany();
    
    console.log('Fetching chats...');
    const chats = await sourceClient.query.chat.findMany();
    
    console.log('Fetching messages...');
    const messages = await sourceClient.query.message.findMany();
    
    console.log('Fetching votes...');
    const votes = await sourceClient.query.vote.findMany();
    
    console.log('Fetching documents...');
    const documents = await sourceClient.query.document.findMany();
    
    console.log('Fetching suggestions...');
    const suggestions = await sourceClient.query.suggestion.findMany();
    
    console.log('Fetching streams...');
    const streams = await sourceClient.query.stream.findMany();
    
    console.log('Fetching settings...');
    const settings = await sourceClient.query.setting.findMany();

    // Insert data into the new database in the correct order to respect foreign key constraints
    console.log('\nStarting insertion into target database...');
    
    // 1. First, insert users (no dependencies)
    console.log('Inserting users...');
    if (users.length > 0) {
      await targetClient
        .insert(schema.user)
        .values(users)
        .onConflictDoNothing();
      console.log(`Inserted ${users.length} users`);
    } else {
      console.log('No users to insert');
    }
    
    // 2. Insert prompts (no dependencies)
    console.log('Inserting prompts...');
    if (prompts.length > 0) {
      await targetClient
        .insert(schema.prompt)
        .values(prompts)
        .onConflictDoNothing();
      console.log(`Inserted ${prompts.length} prompts`);
    } else {
      console.log('No prompts to insert');
    }
    
    // 3. Insert chats (depends on users)
    console.log('Inserting chats...');
    if (chats.length > 0) {
      await targetClient
        .insert(schema.chat)
        .values(chats)
        .onConflictDoNothing();
      console.log(`Inserted ${chats.length} chats`);
    } else {
      console.log('No chats to insert');
    }
    
    // 4. Insert messages (depends on chats and prompts)
    console.log('Inserting messages...');
    // Messages may be large, so batch them if needed
    const batchSize = 500;
    for (let i = 0; i < messages.length; i += batchSize) {
      const batch = messages.slice(i, i + batchSize);
      if (batch.length > 0) {
        await targetClient
          .insert(schema.message)
          .values(batch)
          .onConflictDoNothing();
      }
      console.log(
        `Inserted messages batch ${i / batchSize + 1}/${Math.ceil(messages.length / batchSize)}`,
      );
    }
    console.log(`Inserted ${messages.length} messages total`);
    
    // 5. Insert votes (depends on chats and messages)
    console.log('Inserting votes...');
    if (votes.length > 0) {
      await targetClient
        .insert(schema.vote)
        .values(votes)
        .onConflictDoNothing();
      console.log(`Inserted ${votes.length} votes`);
    } else {
      console.log('No votes to insert');
    }
    
    // 6. Insert documents (depends on users)
    console.log('Inserting documents...');
    if (documents.length > 0) {
      await targetClient
        .insert(schema.document)
        .values(documents)
        .onConflictDoNothing();
      console.log(`Inserted ${documents.length} documents`);
    } else {
      console.log('No documents to insert');
    }
    
    // 7. Insert suggestions (depends on documents and users)
    console.log('Inserting suggestions...');
    if (suggestions.length > 0) {
      await targetClient
        .insert(schema.suggestion)
        .values(suggestions)
        .onConflictDoNothing();
      console.log(`Inserted ${suggestions.length} suggestions`);
    } else {
      console.log('No suggestions to insert');
    }
    
    // 8. Insert streams (depends on chats)
    console.log('Inserting streams...');
    if (streams.length > 0) {
      await targetClient
        .insert(schema.stream)
        .values(streams)
        .onConflictDoNothing();
      console.log(`Inserted ${streams.length} streams`);
    } else {
      console.log('No streams to insert');
    }
    
    // 9. Insert settings (no dependencies)
    console.log('Inserting settings...');
    if (settings.length > 0) {
      await targetClient
        .insert(schema.setting)
        .values(settings)
        .onConflictDoNothing();
      console.log(`Inserted ${settings.length} settings`);
    } else {
      console.log('No settings to insert');
    }

    console.log('\nData migration completed successfully!');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    // Close connections
    console.log('Closing database connections...');
    await sourceDb.end();
    await targetDb.end();
  }
}

main()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch((err) => {
    console.error('Migration failed:', err);
    process.exit(1);
  });