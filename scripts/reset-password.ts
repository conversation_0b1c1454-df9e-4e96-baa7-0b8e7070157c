#!/usr/bin/env tsx

import { config } from 'dotenv';
import { resolve } from 'node:path';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { user } from '../lib/db/schema';
import { generateHashedPassword } from '../lib/db/utils';
import { eq } from 'drizzle-orm';

// Load environment variables from .env.local
config({ path: resolve(process.cwd(), '.env.local') });

async function resetPassword(email: string, newPassword: string) {
  // Check if POSTGRES_URL is defined
  if (!process.env.POSTGRES_URL) {
    console.error('Error: POSTGRES_URL environment variable is not defined');
    console.error('Make sure your .env.local file contains this variable');
    process.exit(1);
  }

  // Initialize Drizzle with the database connection
  const client = postgres(process.env.POSTGRES_URL);
  const db = drizzle(client);

  try {
    // Verify the email exists
    const users = await db.select().from(user).where(eq(user.email, email));

    if (users.length === 0) {
      console.error(`User with email ${email} not found`);
      process.exit(1);
    }

    // Hash the new password
    const hashedPassword = generateHashedPassword(newPassword);

    // Update the user's password
    await db
      .update(user)
      .set({ password: hashedPassword })
      .where(eq(user.email, email));

    console.log(`Password for user ${email} has been reset successfully`);
    process.exit(0);
  } catch (error) {
    console.error('Failed to reset password:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await client.end();
  }
}

// Script entry point
const args = process.argv.slice(2);
if (args.length !== 2) {
  console.error(
    'Usage: pnpm tsx scripts/reset-password.ts <email> <new-password>',
  );
  process.exit(1);
}

const [email, newPassword] = args;
resetPassword(email, newPassword);
