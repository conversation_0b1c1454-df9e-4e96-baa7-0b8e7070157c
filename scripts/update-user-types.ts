import { createClient } from '@vercel/postgres';
import 'dotenv/config';

async function updateUserTypes() {
  const client = createClient({ connectionString: process.env.POSTGRES_URL });
  await client.connect();

  try {
    console.log('Starting user type update');

    // Add type column if it doesn't exist (should already be done by migration)
    await client.sql`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_name = 'User' AND column_name = 'type'
        ) THEN
          ALTER TABLE "User" ADD COLUMN "type" varchar DEFAULT 'user' NOT NULL;
        END IF;
      END $$;
    `;

    // Check for guest users by email pattern and update their type to 'guest'
    const guestResult = await client.sql`
      UPDATE "User"
      SET "type" = 'guest'
      WHERE "email" ~ '^guest-\\d+$'
      RETURNING id, email, type;
    `;

    console.log(`Updated ${guestResult.rowCount} guest users`);

    // Update all other users to 'user' type if not already set
    const regularResult = await client.sql`
      UPDATE "User"
      SET "type" = 'user'
      WHERE "type" IS NULL OR "type" = 'regular'
      RETURNING id, email, type;
    `;

    console.log(`Updated ${regularResult.rowCount} regular users to type 'user'`);

    console.log('User types update completed');
  } catch (error) {
    console.error('Error updating user types:', error);
  } finally {
    await client.end();
  }
}

updateUserTypes().catch(console.error);