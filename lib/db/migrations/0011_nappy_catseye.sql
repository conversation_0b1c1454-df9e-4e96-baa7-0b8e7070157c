CREATE TABLE IF NOT EXISTS "Setting" (
	"key" varchar(255) PRIMARY KEY NOT NULL,
	"value" text DEFAULT '',
	"updatedAt" timestamp DEFAULT now() NOT NULL
);

-- Insert default settings if they don't exist
INSERT INTO "Setting" ("key", "value", "updatedAt")
VALUES ('defaultModelId', 'grok-2', now())
ON CONFLICT ("key") DO NOTHING;

INSERT INTO "Setting" ("key", "value", "updatedAt")
VALUES ('defaultPromptId', '', now())
ON CONFLICT ("key") DO NOTHING;