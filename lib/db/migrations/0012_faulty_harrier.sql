CREATE TABLE IF NOT EXISTS "SubPrompt" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"parentPromptId" uuid NOT NULL,
	"name" text NOT NULL,
	"content" text NOT NULL,
	"createdAt" timestamp DEFAULT now(),
	"isDeleted" boolean DEFAULT false NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SubPrompt" ADD CONSTRAINT "SubPrompt_parentPromptId_Prompt_id_fk" FOREIGN KEY ("parentPromptId") REFERENCES "public"."Prompt"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
