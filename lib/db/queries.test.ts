import { updateChatTitleById } from './queries';
import { chat } from './schema';
import { eq } from 'drizzle-orm';

// Mock postgres and drizzle
jest.mock('postgres', () => {
  const mClient = {
    // Mock any methods that are actually called by drizzle or your queries
    // For example, if drizzle calls .query() or .end() on the client
    query: jest.fn(),
    end: jest.fn(),
    // Mock other necessary client methods or properties
  };
  return jest.fn(() => mClient);
});

const mockDbUpdate = jest.fn().mockReturnThis();
const mockDbSet = jest.fn().mockReturnThis();
const mockDbWhere = jest.fn().mockReturnThis();
const mockDbReturning = jest.fn();

jest.mock('drizzle-orm/postgres-js', () => ({
  drizzle: jest.fn(() => ({
    update: mockDbUpdate,
    // Add other db methods if your function uses them, e.g., select, insert, delete
  })),
}));

describe('updateChatTitleById', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Setup mock implementations for chained calls
    // Ensure `update` returns an object that has `set`, which returns an object that has `where`, etc.
    mockDbUpdate.mockReturnValue({
      set: mockDbSet,
    });
    mockDbSet.mockReturnValue({
      where: mockDbWhere,
    });
    mockDbWhere.mockReturnValue({
      returning: mockDbReturning,
    });
  });

  it('should call db.update with correct parameters and return the updated chat', async () => {
    const chatId = 'test-chat-id';
    const newTitle = 'New Chat Title';
    const expectedUpdatedChat = { id: chatId, title: newTitle, userId: 'user1', visibility: 'private', createdAt: new Date() };

    // Mock the returning value for this specific test case
    mockDbReturning.mockResolvedValueOnce([expectedUpdatedChat]);

    const result = await updateChatTitleById({ chatId, title: newTitle });

    expect(mockDbUpdate).toHaveBeenCalledWith(chat);
    expect(mockDbSet).toHaveBeenCalledWith({ title: newTitle });
    // Note: eq(chat.id, chatId) creates a specific SQL object.
    // For simplicity in this mock, we might check if where was called.
    // For more robust tests, you might need a way to inspect the SQL object or mock `eq` as well.
    expect(mockDbWhere).toHaveBeenCalledWith(eq(chat.id, chatId));
    expect(mockDbReturning).toHaveBeenCalledTimes(1);
    expect(result).toEqual(expectedUpdatedChat);
  });

  it('should handle database errors and log them', async () => {
    const chatId = 'test-chat-id-error';
    const newTitle = 'Error Title';
    const dbError = new Error('Database update failed');

    // Mock the returning value to throw an error
    mockDbReturning.mockRejectedValueOnce(dbError);

    // Mock console.error
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    await expect(updateChatTitleById({ chatId, title: newTitle })).rejects.toThrow(dbError);

    expect(mockDbUpdate).toHaveBeenCalledWith(chat);
    expect(mockDbSet).toHaveBeenCalledWith({ title: newTitle });
    expect(mockDbWhere).toHaveBeenCalledWith(eq(chat.id, chatId));
    expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to update chat title in database', dbError);

    // Restore console.error
    consoleErrorSpy.mockRestore();
  });
});
