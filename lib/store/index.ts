'use client';

import { atom } from 'jotai';
import type { Prompt } from '@/lib/db/schema';

// Chat model related atoms
export const selectedChatModelAtom = atom<string | null>(null);

// Prompt related atoms
export const selectedPromptAtom = atom<Prompt | null>(null);

// User type related atoms
export const isAdminAtom = atom<boolean>(false);

// Default values related atoms
export const defaultModelIdAtom = atom<string | null>(null);
export const defaultPromptAtom = atom<Prompt | null>(null);

// Loading state
export const isLoadingAtom = atom<boolean>(true);