import { describe, it, expect } from '@jest/globals';
import {
  substitutePromptVariables,
  extractVariablePlaceholders,
  validatePromptVariables,
  parsePromptVariables,
} from './prompt-variables';

describe('prompt-variables', () => {
  describe('substitutePromptVariables', () => {
    it('should substitute single variable', () => {
      const content = 'Hello {{name}}, welcome to our service!';
      const variables = { name: '<PERSON>' };
      const result = substitutePromptVariables(content, variables);
      expect(result).toBe('Hello John, welcome to our service!');
    });

    it('should substitute multiple variables', () => {
      const content =
        'Hello {{name}}, you have {{count}} messages from {{sender}}.';
      const variables = { name: '<PERSON>', count: '5', sender: 'Bob' };
      const result = substitutePromptVariables(content, variables);
      expect(result).toBe('Hello Alice, you have 5 messages from <PERSON>.');
    });

    it('should handle missing variables by keeping placeholder', () => {
      const content = 'Hello {{name}}, your {{status}} is pending.';
      const variables = { name: '<PERSON>' };
      const result = substitutePromptVariables(content, variables);
      expect(result).toBe('Hello John, your {{status}} is pending.');
    });

    it('should handle empty variables object', () => {
      const content = 'Hello {{name}}, welcome!';
      const variables = {};
      const result = substitutePromptVariables(content, variables);
      expect(result).toBe('Hello {{name}}, welcome!');
    });

    it('should handle content without variables', () => {
      const content = 'Hello world, no variables here!';
      const variables = { name: 'John' };
      const result = substitutePromptVariables(content, variables);
      expect(result).toBe('Hello world, no variables here!');
    });

    it('should handle empty content', () => {
      const content = '';
      const variables = { name: 'John' };
      const result = substitutePromptVariables(content, variables);
      expect(result).toBe('');
    });

    it('should handle multiline content', () => {
      const content = `{{intro}}

You are an AI assistant. {{best_practices}}

Please help the user with their request.`;
      const variables = {
        intro: 'Welcome to our AI service!',
        best_practices: 'Always be helpful and accurate.',
      };
      const result = substitutePromptVariables(content, variables);
      expect(result).toBe(`Welcome to our AI service!

You are an AI assistant. Always be helpful and accurate.

Please help the user with their request.`);
    });
  });

  describe('extractVariablePlaceholders', () => {
    it('should extract single variable', () => {
      const content = 'Hello {{name}}, welcome!';
      const result = extractVariablePlaceholders(content);
      expect(result).toEqual(['name']);
    });

    it('should extract multiple variables', () => {
      const content = 'Hello {{name}}, you have {{count}} messages.';
      const result = extractVariablePlaceholders(content);
      expect(result).toEqual(['name', 'count']);
    });

    it('should remove duplicates', () => {
      const content = 'Hello {{name}}, {{name}} is your username.';
      const result = extractVariablePlaceholders(content);
      expect(result).toEqual(['name']);
    });

    it('should return empty array for no variables', () => {
      const content = 'Hello world, no variables here!';
      const result = extractVariablePlaceholders(content);
      expect(result).toEqual([]);
    });

    it('should handle empty content', () => {
      const content = '';
      const result = extractVariablePlaceholders(content);
      expect(result).toEqual([]);
    });
  });

  describe('validatePromptVariables', () => {
    it('should validate when all variables are provided', () => {
      const content = 'Hello {{name}}, you have {{count}} messages.';
      const variables = { name: 'John', count: '5' };
      const result = validatePromptVariables(content, variables);
      expect(result).toEqual({ isValid: true, missingVariables: [] });
    });

    it('should detect missing variables', () => {
      const content = 'Hello {{name}}, you have {{count}} messages.';
      const variables = { name: 'John' };
      const result = validatePromptVariables(content, variables);
      expect(result).toEqual({ isValid: false, missingVariables: ['count'] });
    });

    it('should handle no variables needed', () => {
      const content = 'Hello world, no variables here!';
      const variables = {};
      const result = validatePromptVariables(content, variables);
      expect(result).toEqual({ isValid: true, missingVariables: [] });
    });
  });

  describe('parsePromptVariables', () => {
    it('should parse valid JSON', () => {
      const json = '{"name": "John", "count": "5"}';
      const result = parsePromptVariables(json);
      expect(result).toEqual({ name: 'John', count: '5' });
    });

    it('should handle invalid JSON', () => {
      const json = 'invalid json';
      const result = parsePromptVariables(json);
      expect(result).toEqual({});
    });

    it('should handle null input', () => {
      const result = parsePromptVariables(null);
      expect(result).toEqual({});
    });

    it('should handle undefined input', () => {
      const result = parsePromptVariables(undefined);
      expect(result).toEqual({});
    });

    it('should handle empty string', () => {
      const result = parsePromptVariables('');
      expect(result).toEqual({});
    });
  });
});
