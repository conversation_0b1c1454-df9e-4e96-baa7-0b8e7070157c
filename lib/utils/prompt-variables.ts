/**
 * Utility functions for handling prompt variables and substitutions
 */

export interface PromptVariables {
  [key: string]: string;
}

/**
 * Substitutes variables in prompt content using the format {{variableName}}
 * @param content - The prompt content containing variable placeholders
 * @param variables - Object containing variable key-value pairs
 * @returns The content with variables substituted
 */
export function substitutePromptVariables(
  content: string,
  variables: PromptVariables = {},
): string {
  if (!content || typeof content !== 'string') {
    return content || '';
  }

  // Replace all occurrences of {{variableName}} with the corresponding value
  return content.replace(/\{\{(\w+)\}\}/g, (match, variableName) => {
    const value = variables[variableName];

    // If variable is not found, return the original placeholder
    if (value === undefined || value === null) {
      console.warn(
        `Prompt variable '${variableName}' not found, keeping placeholder`,
      );
      return match;
    }

    return value;
  });
}

/**
 * Extracts all variable placeholders from prompt content
 * @param content - The prompt content to analyze
 * @returns Array of variable names found in the content
 */
export function extractVariablePlaceholders(content: string): string[] {
  if (!content || typeof content !== 'string') {
    return [];
  }

  const matches = content.match(/\{\{(\w+)\}\}/g);
  if (!matches) {
    return [];
  }

  // Extract variable names and remove duplicates
  return [...new Set(matches.map((match) => match.slice(2, -2)))];
}

/**
 * Validates that all required variables are provided
 * @param content - The prompt content containing variable placeholders
 * @param variables - Object containing variable key-value pairs
 * @returns Object with validation result and missing variables
 */
export function validatePromptVariables(
  content: string,
  variables: PromptVariables = {},
): { isValid: boolean; missingVariables: string[] } {
  const requiredVariables = extractVariablePlaceholders(content);
  const missingVariables = requiredVariables.filter(
    (varName) =>
      variables[varName] === undefined || variables[varName] === null,
  );

  return {
    isValid: missingVariables.length === 0,
    missingVariables,
  };
}

/**
 * Safely parses variables from JSON string or object, returning empty object on error
 * @param variablesData - JSON string or object containing variables
 * @returns Parsed variables object or empty object
 */
export function parsePromptVariables(
  variablesData: string | object | null | undefined,
): PromptVariables {
  if (!variablesData) {
    return {};
  }

  // If it's already an object, return it directly
  if (typeof variablesData === 'object') {
    return variablesData as PromptVariables;
  }

  // If it's a string, try to parse it as JSON
  try {
    const parsed = JSON.parse(variablesData);
    return typeof parsed === 'object' && parsed !== null ? parsed : {};
  } catch (error) {
    console.warn('Failed to parse prompt variables JSON:', error);
    return {};
  }
}
