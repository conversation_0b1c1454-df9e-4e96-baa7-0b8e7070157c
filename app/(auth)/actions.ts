'use server';

import { z } from 'zod';
import { createUser, getUser, updateUserPassword } from '@/lib/db/queries';
import { generateHashedPassword } from '@/lib/db/utils';
import { generatePasswordResetToken, verifyPasswordResetToken } from '@/lib/auth/tokens';
import { sendPasswordResetEmail } from '@/lib/email/mailer';

import { signIn } from './auth';

const authFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

const resetPasswordFormSchema = z.object({
  email: z.string().email(),
});

const confirmResetPasswordFormSchema = z.object({
  token: z.string().min(1),
  password: z.string().min(8),
  confirmPassword: z.string().min(8),
});

export interface LoginActionState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data';
}

export const login = async (
  _: LoginActionState,
  formData: FormData,
): Promise<LoginActionState> => {
  try {
    const validatedData = authFormSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
    });

    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }

    return { status: 'failed' };
  }
};

export interface RegisterActionState {
  status:
    | 'idle'
    | 'in_progress'
    | 'success'
    | 'failed'
    | 'user_exists'
    | 'invalid_data';
}

export const register = async (
  _: RegisterActionState,
  formData: FormData,
): Promise<RegisterActionState> => {
  try {
    const validatedData = authFormSchema.parse({
      email: formData.get('email'),
      password: formData.get('password'),
    });

    const [user] = await getUser(validatedData.email);

    if (user) {
      return { status: 'user_exists' } as RegisterActionState;
    }
    await createUser(validatedData.email, validatedData.password, 'user');
    await signIn('credentials', {
      email: validatedData.email,
      password: validatedData.password,
      redirect: false,
    });

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }

    return { status: 'failed' };
  }
};

export interface ResetPasswordActionState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data';
}

/**
 * Server action to request a password reset
 * Sends a reset email if the user exists, fails silently if not
 */
export const requestPasswordReset = async (
  _: ResetPasswordActionState,
  formData: FormData,
): Promise<ResetPasswordActionState> => {
  try {
    const validatedData = resetPasswordFormSchema.parse({
      email: formData.get('email'),
    });

    // Check if user exists
    const [user] = await getUser(validatedData.email);

    // If user exists, send reset email
    if (user) {
      // Generate reset token
      const resetToken = await generatePasswordResetToken(user.email);

      // Send email with reset link
      await sendPasswordResetEmail(user.email, resetToken);
    }

    // Always return success to prevent email enumeration
    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }

    console.error('Password reset request failed:', error);
    return { status: 'failed' };
  }
};

export interface ResetPasswordConfirmActionState {
  status: 'idle' | 'in_progress' | 'success' | 'failed' | 'invalid_data' | 'token_expired';
}

/**
 * Server action to confirm and process password reset
 */
export const resetPassword = async (
  _: ResetPasswordConfirmActionState,
  formData: FormData,
): Promise<ResetPasswordConfirmActionState> => {
  try {
    const token = formData.get('token') as string;
    const password = formData.get('password') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    // Validate form input
    const validatedData = confirmResetPasswordFormSchema.parse({
      token,
      password,
      confirmPassword,
    });

    // Check that passwords match (validation already ensures they're both min 8 chars)
    if (validatedData.password !== validatedData.confirmPassword) {
      return { status: 'invalid_data' };
    }

    // Verify token
    const email = await verifyPasswordResetToken(token);
    if (!email) {
      return { status: 'token_expired' };
    }

    // Get user from email
    const [user] = await getUser(email);
    if (!user) {
      return { status: 'failed' };
    }

    // Hash the new password
    const hashedPassword = generateHashedPassword(validatedData.password);

    // Update user record with new password - using the updateUserPassword utility
    await updateUserPassword(user.id, hashedPassword);

    return { status: 'success' };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: 'invalid_data' };
    }

    console.error('Password reset failed:', error);
    return { status: 'failed' };
  }
};
