import { auth } from '@/app/(auth)/auth';
import {
  createSubPrompt,
  getSubPromptsByParentId,
  updateSubPrompt,
  deleteSubPrompt,
} from '@/lib/db/queries';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const parentId = searchParams.get('parentId');

  if (!parentId) {
    return NextResponse.json(
      { error: 'parentId is required' },
      { status: 400 },
    );
  }

  try {
    const subPrompts = await getSubPromptsByParentId({ parentId });
    return NextResponse.json(subPrompts);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to fetch sub-prompts' },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { parentPromptId, name, content } = await request.json();

  if (!parentPromptId || !name || !content) {
    return NextResponse.json(
      { error: 'parentPromptId, name, and content are required' },
      { status: 400 },
    );
  }

  try {
    const subPrompt = await createSubPrompt({ parentPromptId, name, content });
    return NextResponse.json(subPrompt);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to create sub-prompt' },
      { status: 500 },
    );
  }
}

export async function PATCH(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { id, name, content } = await request.json();

  if (!id || !name || !content) {
    return NextResponse.json(
      { error: 'id, name, and content are required' },
      { status: 400 },
    );
  }

  try {
    const subPrompt = await updateSubPrompt({ id, name, content });
    return NextResponse.json(subPrompt);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to update sub-prompt' },
      { status: 500 },
    );
  }
}

export async function DELETE(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { id } = await request.json();

  if (!id) {
    return NextResponse.json(
      { error: 'id is required' },
      { status: 400 },
    );
  }

  try {
    const result = await deleteSubPrompt({ id });
    return NextResponse.json(result);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to delete sub-prompt' },
      { status: 500 },
    );
  }
}