import { auth } from '@/app/(auth)/auth';
import { deleteMessageById, getChatById } from '@/lib/db/queries';

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');
  const messageId = searchParams.get('messageId');

  if (!chatId || !messageId) {
    return new Response('chatId and messageId are required', { status: 400 });
  }

  const session = await auth();

  if (!session || !session.user || !session.user.email) {
    return new Response('Unauthorized', { status: 401 });
  }

  const chat = await getChatById({ id: chatId });

  if (!chat) {
    return new Response('Chat not found', { status: 404 });
  }

  // Check if the user is the owner of the chat
  if (chat.userId !== session.user.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  await deleteMessageById({
    chatId,
    messageId,
  });

  return new Response('Message deleted', { status: 200 });
}
