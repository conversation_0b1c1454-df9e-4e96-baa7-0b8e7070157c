import { auth } from '@/app/(auth)/auth';
import {
  createPrompt,
  getPrompts,
  updatePrompt,
  deletePrompt,
} from '@/lib/db/queries';
import { NextResponse } from 'next/server';

export async function GET() {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const prompts = await getPrompts();
    return NextResponse.json(prompts);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to fetch prompts' },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { name, content, variables } = await request.json();

  try {
    const prompt = await createPrompt({ name, content, variables });
    return NextResponse.json(prompt);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to create prompt' },
      { status: 500 },
    );
  }
}

export async function PATCH(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { id, name, content, variables } = await request.json();

  try {
    const prompt = await updatePrompt({ id, name, content, variables });
    return NextResponse.json(prompt);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to update prompt' },
      { status: 500 },
    );
  }
}

export async function DELETE(request: Request) {
  const session = await auth();

  if (!session?.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  const { id } = await request.json();

  try {
    const result = await deletePrompt({ id });
    return NextResponse.json(result);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to delete prompt' },
      { status: 500 },
    );
  }
}
