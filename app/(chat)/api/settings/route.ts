import { auth } from '@/app/(auth)/auth';
import { 
  getSetting, 
  getAllSettings,
  updateSetting,
  createSetting
} from '@/lib/db/queries';
import { NextResponse } from 'next/server';

export async function GET() {
  const session = await auth();

  if (!session?.user || session.user.type !== 'admin') {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const settings = await getAllSettings();
    return NextResponse.json(settings);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 },
    );
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user || session.user.type !== 'admin') {
    return new Response('Unauthorized', { status: 401 });
  }

  const { key, value } = await request.json();

  try {
    // Check if setting already exists
    const existingSetting = await getSetting(key);
    
    if (existingSetting && existingSetting.length > 0) {
      const updated = await updateSetting(key, value);
      return NextResponse.json(updated);
    } else {
      const created = await createSetting(key, value);
      return NextResponse.json(created);
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Failed to update setting' },
      { status: 500 },
    );
  }
}