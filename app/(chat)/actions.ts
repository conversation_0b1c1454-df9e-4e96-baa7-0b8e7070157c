'use server';

import { generateText, type UIMessage } from 'ai';
import { cookies } from 'next/headers';
import {
  deleteMessagesByChatIdAfterTimestamp,
  getMessageById,
  updateChatVisiblityById,
  deletePrompt,
  getPrompts,
  getSetting,
  getPromptById,
  updateSetting,
  createSetting,
  updateChatTitleById,
} from '@/lib/db/queries';
import type { VisibilityType } from '@/components/visibility-selector';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { revalidatePath } from 'next/cache';
import { myProvider } from '@/lib/ai/providers';

export async function saveChatModelAsCookie(model: string) {
  const cookieStore = await cookies();
  cookieStore.set('selected-model', model);
}

export async function savePromptAsCookie(promptId: string) {
  const cookieStore = await cookies();
  cookieStore.set('selected-prompt', promptId);
}

export async function generateTitleFromUserMessage({
  message,
}: {
  message: UIMessage;
}) {
  const { text: title } = await generateText({
    model: myProvider.languageModel('grok-2'),
    system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,
    prompt: JSON.stringify(message),
  });

  return title;
}

export async function deleteTrailingMessages({ id }: { id: string }) {
  const [message] = await getMessageById({ id });

  await deleteMessagesByChatIdAfterTimestamp({
    chatId: message.chatId,
    timestamp: message.createdAt,
  });
}

export async function updateChatVisibility({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: VisibilityType;
}) {
  await updateChatVisiblityById({ chatId, visibility });
}

export async function deletePromptAction({ id }: { id: string }) {
  // Check if the prompt to be deleted is currently selected
  const cookieStore = await cookies();
  const selectedPromptId = cookieStore.get('selected-prompt')?.value;

  // Delete (soft delete) the prompt
  const result = await deletePrompt({ id });

  // If the deleted prompt was selected, reset to a different one
  if (selectedPromptId === id) {
    // Get the first available prompt that's not deleted
    const availablePrompts = await getPrompts();
    if (availablePrompts.length > 0) {
      // Set the first available prompt as selected
      cookieStore.set('selected-prompt', availablePrompts[0].id);
    } else {
      // If no prompts are available, remove the cookie
      cookieStore.delete('selected-prompt');
    }
  }

  return result;
}

export async function getDefaultModelSetting() {
  try {
    const settings = await getSetting('defaultModelId');
    return settings.length > 0 ? settings[0].value : DEFAULT_CHAT_MODEL;
  } catch (error) {
    console.error('Failed to get default model setting:', error);
    return DEFAULT_CHAT_MODEL;
  }
}

export async function getDefaultPromptSetting() {
  try {
    const settings = await getSetting('defaultPromptId');
    if (settings.length > 0 && settings[0].value) {
      const promptId = settings[0].value;
      const prompt = await getPromptById({ id: promptId });
      return prompt || null;
    }
    return null;
  } catch (error) {
    console.error('Failed to get default prompt setting:', error);
    return null;
  }
}

export async function updateDefaultSettings(modelId: string, promptId: string) {
  // Direct database update approach instead of fetch API
  try {
    // Update model setting
    const modelSetting = await getSetting('defaultModelId');
    if (modelSetting.length > 0) {
      await updateSetting('defaultModelId', modelId);
    } else {
      await createSetting('defaultModelId', modelId);
    }

    // Update prompt setting
    const promptSetting = await getSetting('defaultPromptId');
    if (promptSetting.length > 0) {
      await updateSetting('defaultPromptId', promptId);
    } else {
      await createSetting('defaultPromptId', promptId);
    }

    revalidatePath('/');
    return { success: true };
  } catch (error) {
    console.error('Failed to update settings:', error);
    throw new Error('Failed to update settings');
  }
}

export async function renameChat({
  chatId,
  newTitle,
}: {
  chatId: string;
  newTitle: string;
}) {
  const result = await updateChatTitleById({ chatId, title: newTitle });
  revalidatePath('/');
  revalidatePath(`/chat/${chatId}`);
  return result;
}
