import { renameChat } from './(chat)/actions'; // Adjusted import path
import { updateChatTitleById } from '@/lib/db/queries';
import { revalidatePath } from 'next/cache';

// Mock dependencies
jest.mock('@/lib/db/queries', () => ({
  updateChatTitleById: jest.fn(),
}));

jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

// Need to mock actions if they are imported from the same file for other tests,
// or if there's a circular dependency issue in testing.
// For this specific test, we are testing `renameChat` itself.
// If `app/(chat)/actions.ts` exports other actions that might interfere or
// are hard to mock, this might need adjustment.
// jest.mock('./(chat)/actions', () => ({
//   ...jest.requireActual('./(chat)/actions'), // Import and retain other action implementations
//   renameChat: jest.requireActual('./(chat)/actions').renameChat, // Test the actual renameChat
//   // Potentially mock other specific actions if needed for isolation
// }));


describe('renameChat server action', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('should call updateChatTitleById with correct parameters and revalidate paths', async () => {
    const chatId = 'test-chat-id';
    const newTitle = 'New Test Title';
    const mockUpdatedChat = { id: chatId, title: newTitle, userId: 'user1', visibility: 'private', createdAt: new Date() };

    // Setup mock implementation for updateChatTitleById
    (updateChatTitleById as jest.Mock).mockResolvedValueOnce(mockUpdatedChat);

    const result = await renameChat({ chatId, newTitle });

    // Verify updateChatTitleById was called correctly
    expect(updateChatTitleById).toHaveBeenCalledWith({ chatId, title: newTitle });
    expect(updateChatTitleById).toHaveBeenCalledTimes(1);

    // Verify revalidatePath was called correctly
    expect(revalidatePath).toHaveBeenCalledWith('/');
    expect(revalidatePath).toHaveBeenCalledWith(`/chat/${chatId}`);
    expect(revalidatePath).toHaveBeenCalledTimes(2);

    // Verify the result
    expect(result).toEqual(mockUpdatedChat);
  });

  it('should propagate errors from updateChatTitleById', async () => {
    const chatId = 'error-chat-id';
    const newTitle = 'Error Inducing Title';
    const dbError = new Error('Database error');

    // Setup mock implementation for updateChatTitleById to throw an error
    (updateChatTitleById as jest.Mock).mockRejectedValueOnce(dbError);

    await expect(renameChat({ chatId, newTitle })).rejects.toThrow(dbError);

    // Verify updateChatTitleById was called
    expect(updateChatTitleById).toHaveBeenCalledWith({ chatId, title: newTitle });
    expect(updateChatTitleById).toHaveBeenCalledTimes(1);

    // Verify revalidatePath was NOT called in case of error before it
    expect(revalidatePath).not.toHaveBeenCalled();
  });
});
