'use client';

import { use<PERSON>tom } from 'jotai';
import { 
  selectedChatModel<PERSON>tom, 
  selectedPrompt<PERSON>tom, 
  isAdminAtom,
  defaultModelIdAtom,
  defaultPromptAtom,
  isLoadingAtom
} from '@/lib/store';
import type { Prompt } from '@/lib/db/schema';

export function useAppState() {
  const [selectedChatModel, setSelectedChatModel] = useAtom(selectedChatModelAtom);
  const [selectedPrompt, setSelectedPrompt] = useAtom(selectedPromptAtom);
  const [isAdmin, setIsAdmin] = useAtom(isAdminAtom);
  const [defaultModelId, setDefaultModelId] = useAtom(defaultModelIdAtom);
  const [defaultPrompt, setDefaultPrompt] = useAtom(defaultPromptAtom);
  const [isLoading, setIsLoading] = useAtom(isLoadingAtom);

  const selectModel = (modelId: string) => {
    setSelectedChatModel(modelId);
  };

  const selectPrompt = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
  };

  return {
    // Model state
    selectedChatModel,
    setSelectedChatModel,
    selectModel,

    // Prompt state
    selectedPrompt,
    setSelectedPrompt,
    selectPrompt,

    // User state
    isAdmin,
    setIsAdmin,

    // Default values
    defaultModelId,
    setDefaultModelId,
    defaultPrompt,
    setDefaultPrompt,

    // Loading state
    isLoading,
    setIsLoading
  };
}