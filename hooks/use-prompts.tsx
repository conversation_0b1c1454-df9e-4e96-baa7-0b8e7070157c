'use client';

import { useEffect, useState } from 'react';
import type { Prompt } from '@/lib/db/schema';

let cachedPrompts: Prompt[] | null = null;

export function usePrompts() {
  const [prompts, setPrompts] = useState<Prompt[]>(cachedPrompts || []);
  const [loading, setLoading] = useState(!cachedPrompts);

  // Function to fetch prompts and return them
  const fetchPrompts = async (): Promise<Prompt[]> => {
    const res = await fetch('/api/prompts');
    const data = await res.json();
    return data;
  };

  // Function to reload prompts and update state
  const reload = async () => {
    setLoading(true);
    cachedPrompts = null;
    const data = await fetchPrompts();
    cachedPrompts = data;
    setPrompts(data);
    setLoading(false);
    return data; // Return the prompts for immediate use
  };

  useEffect(() => {
    if (cachedPrompts) return;
    reload();
  }, []);

  return { prompts, loading, reload, fetchPrompts };
}
