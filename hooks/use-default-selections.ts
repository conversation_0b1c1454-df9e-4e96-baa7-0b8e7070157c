'use client';

import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { usePrompts } from './use-prompts';
import { useAppState } from './use-app-state';
import {
  saveChatModelAsCookie,
  savePromptAsCookie,
} from '@/app/(chat)/actions';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';

export function useDefaultSelections() {
  const { data: session } = useSession();
  const isUserAdmin = session?.user?.type === 'admin';
  const { prompts } = usePrompts();

  // Use app state for storing defaults and user type
  const {
    defaultModelId,
    setDefaultModelId,
    defaultPrompt,
    setDefaultPrompt,
    isAdmin,
    setIsAdmin,
    isLoading,
    setIsLoading,
  } = useAppState();

  // Update isAdmin when session changes
  useEffect(() => {
    setIsAdmin(isUserAdmin);
  }, [isUserAdmin, setIsAdmin]);

  // Set default prompt to the most recent one if no default exists
  useEffect(() => {
    if (prompts.length > 0 && !defaultPrompt) {
      // First prompt in the array (sorted by createdAt desc)
      setDefaultPrompt(prompts[0]);
      setIsLoading(false);
    } else if (prompts.length === 0) {
      setIsLoading(false);
    }
  }, [prompts, defaultPrompt, setDefaultPrompt, setIsLoading]);

  // Set cookie values for users based on type
  useEffect(() => {
    // Only run once when session is loaded and we have prompts and defaults are loaded
    if (session && prompts.length > 0 && !isLoading) {
      // Use a ref to track if we've already set cookies for this session
      const hasSetCookies = sessionStorage.getItem('has_set_default_cookies');

      if (hasSetCookies !== 'true') {
        if (!isAdmin) {
          // For non-admin users, apply the admin-set default model and prompt
          // Use the defaultModelId from app state (set by admin), or fall back to default
          saveChatModelAsCookie(defaultModelId || DEFAULT_CHAT_MODEL);

          // Use the default prompt set by admin, or fall back to first prompt
          if (defaultPrompt) {
            savePromptAsCookie(defaultPrompt.id);
          } else if (prompts[0]) {
            savePromptAsCookie(prompts[0].id);
          }
        } else {
          // For admin users, only set cookie if it doesn't exist
          const cookieModel = document.cookie
            .split('; ')
            .find((row) => row.startsWith('selected-model='));

          if (!cookieModel) {
            // Even for admins, use the default model if it's not set in cookies
            saveChatModelAsCookie(defaultModelId || DEFAULT_CHAT_MODEL);
          }

          // Check if prompt cookie exists
          const cookiePrompt = document.cookie
            .split('; ')
            .find((row) => row.startsWith('selected-prompt='));

          if (!cookiePrompt && defaultPrompt) {
            savePromptAsCookie(defaultPrompt.id);
          }
        }

        // Mark that we've set cookies for this session
        sessionStorage.setItem('has_set_default_cookies', 'true');
      }
    }
  }, [isAdmin, session, prompts, isLoading, defaultModelId, defaultPrompt]);

  return {
    defaultModelId,
    defaultPrompt,
    isAdmin,
    isLoading,
  };
}
