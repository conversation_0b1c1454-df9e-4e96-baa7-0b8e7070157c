<h1 align="center">Lena AI Prototype</h1>

## Running locally

You will need to use the environment variables [defined in `.env.example`](.env.example) to run this prototype. It's recommended you use [Vercel Environment Variables](https://vercel.com/docs/projects/environment-variables) for this, but a `.env` file is all that is necessary.

> Note: You should not commit your `.env` file or it will expose secrets that will allow others to control access to your various AI and authentication provider accounts.

1. Install Vercel CLI: `npm i -g vercel`
2. Link local instance with Vercel and GitHub accounts (creates `.vercel` directory): `vercel link`
3. Download your environment variables: `vercel env pull`

```bash
pnpm install
pnpm dev
```

Your app template should now be running on [localhost:3000](http://localhost:3000).

## User Types

The application supports three user types:
- **Guest**: Limited access users without an account
- **User**: Registered users with standard access
- **Admin**: Users with administrative privileges

### Creating an Admin User

Run the admin creation script:

```bash
npx tsx scripts/create-admin.ts
```

### Updating User Types

To update existing users in the database to the new type system:

```bash
npx tsx scripts/update-user-types.ts
```

### Resetting User Passwords

If a user forgets their password, you can reset it using:

```bash
npx tsx scripts/reset-password.ts <email> <new-password>
```

For example:
```bash
npx tsx scripts/reset-password.ts <EMAIL> newSecurePassword123
```

Note: This script requires the `.env.local` file with the `POSTGRES_URL` environment variable properly configured.
