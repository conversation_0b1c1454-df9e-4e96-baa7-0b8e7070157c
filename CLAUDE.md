# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Lena AI is a Next.js-based chatbot application built with the AI SDK that allows users to interact with various AI models, create artifacts (code, text, images, spreadsheets), and leverage AI tools. The project is built with Next.js App Router, React Server Components, and Server Actions.

## Development Commands

### Setup and Installation

```bash
# Install dependencies
pnpm install

# Set up environment variables (copy from .env.example)
cp .env.example .env.local
# Fill in required variables in .env.local
```

### Database Management

```bash
# Run database migrations
pnpm db:migrate

# Generate migrations from schema changes
pnpm db:generate

# View database with Drizzle Studio
pnpm db:studio

# Push schema changes to database
pnpm db:push

# Pull schema from database
pnpm db:pull

# Check migration status
pnpm db:check

# Apply new migrations
pnpm db:up
```

### Development

```bash
# Start development server (note - while developing I start the server on my own, you do not need to do this)
pnpm dev

# Build for production (runs migrations first)
pnpm build

# Start production server
pnpm start

# Lint code
pnpm lint

# Fix linting issues
pnpm lint:fix

# Format code with Biome
pnpm format
```

### Testing

```bash
# Run all tests
pnpm test

# Run specific test file
npx playwright test tests/e2e/chat.test.ts

# Run specific test project
npx playwright test --project=e2e

# Run tests with UI
npx playwright test --ui

# Show test report
npx playwright show-report
```

## Project Architecture

### Key Components

1. **App Router Structure**:
   - `app/(auth)` - Authentication pages and API routes
     - Login, registration and password reset flows
     - Authentication configuration in `auth.config.ts` and `auth.ts`
   - `app/(chat)` - Chat interface and related API routes
   - `app/api` - API endpoints

2. **Database**:
   - PostgreSQL with Drizzle ORM
   - Schema defined in `lib/db/schema.ts`
   - Migrations in `lib/db/migrations`

3. **AI Integration**:
   - Multiple model providers (xAI, OpenAI, Anthropic, Mistral)
   - Model definitions in `lib/ai/models.ts`
   - Provider configurations in `lib/ai/providers.ts`

4. **Artifacts System**:
   - Code generation and execution (`artifacts/code`)
   - Text editing (`artifacts/text`)
   - Image generation (`artifacts/image`)
   - Spreadsheet manipulation (`artifacts/sheet`)

5. **UI Components**:
   - Chat interface (`components/chat.tsx`)
   - Artifact display (`components/artifact.tsx`)
   - Various editors (code, text, image, sheet)
   - Core UI components in `components/ui`

### Data Flow

1. User messages are sent via the chat interface
2. Messages are processed through the AI SDK
3. If tool calls are detected, they're routed to appropriate handlers
4. Results are streamed back to the UI
5. Artifacts can be created and manipulated based on AI outputs

## Environment Variables

Required environment variables (see `.env.example` for details):

- `AUTH_SECRET` - Secret for authentication and password reset tokens
- `XAI_API_KEY` - API key for xAI services
- `BLOB_READ_WRITE_TOKEN` - Token for Vercel Blob Store
- `POSTGRES_URL` - URL for PostgreSQL database
- `REDIS_URL` - URL for Redis store
- `MAILERSEND_API_KEY` - API key for MailerSend email service
- `MAILERSEND_SENDER_DOMAIN` - Sender domain for MailerSend
- `NEXT_PUBLIC_APP_URL` - Public URL of the app for generating reset links

## Testing

The project uses Playwright for end-to-end testing:

- Test fixtures in `tests/fixtures.ts`
- E2E tests in `tests/e2e/`
- Route tests in `tests/routes/`
- Page object models in `tests/pages/`

## Common Development Patterns

1. **Server Actions**: Used for database operations and server-side logic
   - Auth actions for login, register, and password reset in `app/(auth)/actions.ts`
   - Chat actions in `app/(chat)/actions.ts`
2. **API Routes**: Used for client-server communication (`app/(chat)/api/`)
3. **Client Components**: UI components that require client-side interactivity
4. **React Server Components**: Server-rendered components for improved performance
5. **Auth Flows**:
   - NextAuth.js for authentication
   - Password reset using JWT tokens with 30 min expiration
   - Email sending via MailerSend API
6. **Global State Management**: The application uses Jotai v2 for global app state
   - Global atom definitions in `lib/store/index.ts`
   - Custom hook `useAppState` in `hooks/use-app-state.ts` provides unified state access
   - Used for sharing state between components like selected models, prompts, and user information
   - Helps ensure component state consistency across the application

## Important Code Constraints

1. **Database Access**: Never import from `lib/db/queries` in client components. These functions are server-only.
   - For client components, use the API routes or dedicated hooks like `usePrompts` that fetch data via API calls
   - Database operations should only be done in Server Components, Server Actions, or API Routes