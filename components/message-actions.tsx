import type { TextPart, UIMessage } from 'ai';
import { useSWRConfig } from 'swr';
import { useCopyToClipboard } from 'usehooks-ts';

import type { Vote } from '@/lib/db/schema';
import { deleteTrailingMessages } from '@/app/(chat)/actions';

import {
  CopyIcon,
  ThumbDownIcon,
  ThumbUpIcon,
  TrashIcon,
  RefreshCcwIcon,
} from './icons';
import { Button } from './ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from './ui/tooltip';
import { memo, useState } from 'react';
import equal from 'fast-deep-equal';
import { toast } from 'sonner';
import type { UseChatHelpers } from '@ai-sdk/react';

interface PureMessageActionsProps {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  allMessages: UIMessage[];
  setMessages: UseChatHelpers['setMessages'];
  reload: UseChatHelpers['reload'];
}

export function PureMessageActions({
  chatId,
  message,
  vote,
  isLoading,
  allMessages,
  setMessages,
  reload,
}: PureMessageActionsProps) {
  const { mutate } = useSWRConfig();
  const [_, copyToClipboard] = useCopyToClipboard();
  const [isRegenerating, setIsRegenerating] = useState(false);

  if (isLoading && !isRegenerating) return null;
  if (message.role === 'user') return null;

  return (
    <TooltipProvider delayDuration={0}>
      <div className="flex flex-row gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="py-1 px-2 h-fit text-muted-foreground"
              variant="outline"
              onClick={async () => {
                const textFromParts = message.parts
                  ?.filter((part): part is TextPart => part.type === 'text')
                  .map((part) => part.text)
                  .join('\n')
                  .trim();

                if (!textFromParts) {
                  toast.error("There's no text to copy!");
                  return;
                }

                await copyToClipboard(textFromParts);
                toast.success('Copied to clipboard!');
              }}
            >
              <CopyIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Copy</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-upvote"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              disabled={vote?.isUpvoted}
              variant="outline"
              onClick={async () => {
                const upvote = fetch('/api/vote', {
                  method: 'PATCH',
                  body: JSON.stringify({
                    chatId,
                    messageId: message.id,
                    type: 'up',
                  }),
                });

                toast.promise(upvote, {
                  loading: 'Upvoting Response...',
                  success: () => {
                    mutate<Array<Vote>>(
                      `/api/vote?chatId=${chatId}`,
                      (currentVotes) => {
                        if (!currentVotes) return [];

                        const votesWithoutCurrent = currentVotes.filter(
                          (vote) => vote.messageId !== message.id,
                        );

                        return [
                          ...votesWithoutCurrent,
                          {
                            chatId,
                            messageId: message.id,
                            isUpvoted: true,
                          },
                        ];
                      },
                      { revalidate: false },
                    );

                    return 'Upvoted Response!';
                  },
                  error: 'Failed to upvote response.',
                });
              }}
            >
              <ThumbUpIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Upvote Response</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-downvote"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              variant="outline"
              disabled={vote && !vote.isUpvoted}
              onClick={async () => {
                const downvote = fetch('/api/vote', {
                  method: 'PATCH',
                  body: JSON.stringify({
                    chatId,
                    messageId: message.id,
                    type: 'down',
                  }),
                });

                toast.promise(downvote, {
                  loading: 'Downvoting Response...',
                  success: () => {
                    mutate<Array<Vote>>(
                      `/api/vote?chatId=${chatId}`,
                      (currentVotes) => {
                        if (!currentVotes) return [];

                        const votesWithoutCurrent = currentVotes.filter(
                          (vote) => vote.messageId !== message.id,
                        );

                        return [
                          ...votesWithoutCurrent,
                          {
                            chatId,
                            messageId: message.id,
                            isUpvoted: false,
                          },
                        ];
                      },
                      { revalidate: false },
                    );

                    return 'Downvoted Response!';
                  },
                  error: 'Failed to downvote response.',
                });
              }}
            >
              <ThumbDownIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Downvote Response</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-regenerate"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              variant="outline"
              disabled={isLoading || isRegenerating}
              onClick={async () => {
                setIsRegenerating(true);
                const currentMessageIndex = allMessages.findIndex(
                  (m) => m.id === message.id,
                );
                let targetUserMessage: UIMessage | null = null;

                if (currentMessageIndex > -1) {
                  for (let i = currentMessageIndex - 1; i >= 0; i--) {
                    if (allMessages[i].role === 'user') {
                      targetUserMessage = allMessages[i];
                      break;
                    }
                  }
                }

                if (!targetUserMessage) {
                  toast.error(
                    'Could not find a previous user message to regenerate from.',
                  );
                  setIsRegenerating(false);
                  return;
                }

                const finalTargetUserMessage = targetUserMessage;

                const promise = new Promise((resolve, reject) => {
                  (async () => {
                    try {
                      await deleteTrailingMessages({
                        id: finalTargetUserMessage.id,
                      });

                      mutate(
                        `/api/chat/messages?chatId=${chatId}`,
                        (cachedMessages: UIMessage[] | undefined) => {
                          if (!cachedMessages) return [];
                          const userMsgIndex = cachedMessages.findIndex(
                            (m) => m.id === finalTargetUserMessage.id,
                          );
                          if (userMsgIndex === -1) return cachedMessages;
                          return cachedMessages.slice(0, userMsgIndex + 1);
                        },
                        { revalidate: false },
                      );

                      setMessages((currentMsgs) => {
                        const userMsgIndex = currentMsgs.findIndex(
                          (m) => m.id === finalTargetUserMessage.id,
                        );
                        if (userMsgIndex === -1) return currentMsgs;
                        return currentMsgs.slice(0, userMsgIndex + 1);
                      });

                      await reload();
                      resolve('Response regenerated successfully!');
                    } catch (error) {
                      console.error('Regeneration failed:', error);
                      reject(new Error('Failed to regenerate response.'));
                    }
                  })();
                });

                toast.promise(promise, {
                  loading: 'Regenerating Response...',
                  success: (msg) => {
                    setIsRegenerating(false);
                    return msg as string;
                  },
                  error: (err) => {
                    setIsRegenerating(false);
                    return err.message;
                  },
                });
              }}
            >
              <RefreshCcwIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Regenerate Response</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              data-testid="message-delete"
              className="py-1 px-2 h-fit text-muted-foreground !pointer-events-auto"
              variant="outline"
              onClick={async () => {
                const deleteRequest = fetch(
                  `/api/message?chatId=${chatId}&messageId=${message.id}`,
                  {
                    method: 'DELETE',
                  },
                );

                toast.promise(deleteRequest, {
                  loading: 'Deleting Message...',
                  success: () => {
                    mutate(
                      `/api/chat/messages?chatId=${chatId}`,
                      (messages: UIMessage[] | undefined) => {
                        if (!messages) return [];
                        return messages.filter((msg) => msg.id !== message.id);
                      },
                      { revalidate: false },
                    );

                    if (setMessages) {
                      setMessages((prevMessages) =>
                        prevMessages.filter((msg) => msg.id !== message.id),
                      );
                    }

                    mutate(`/api/vote?chatId=${chatId}`);

                    return 'Message Deleted!';
                  },
                  error: 'Failed to delete message.',
                });
              }}
            >
              <TrashIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Delete Message</TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}

export const MessageActions = memo(
  PureMessageActions,
  (prevProps, nextProps) => {
    if (prevProps.chatId !== nextProps.chatId) return false;
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;
    if (prevProps.setMessages !== nextProps.setMessages) return false;
    if (prevProps.reload !== nextProps.reload) return false;
    if (!equal(prevProps.allMessages, nextProps.allMessages)) return false;

    return true;
  },
);
