import { motion } from 'framer-motion';
import Image from 'next/image';

export const Greeting = () => {
  return (
    <div
      key="overview"
      className="max-w-3xl mx-auto md:mt-12 px-8 size-full flex flex-col justify-center"
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ delay: 0.5 }}
        className="flex flex-col md:flex-row md:items-center gap-6 mt-2 mb-4"
      >
        <div className="rounded-2xl hidden md:block">
          <Image
            src="/images/icon.png"
            alt="Lena AI"
            width={100}
            height={100}
            className="object-cover"
          />
        </div>

        <div className="flex flex-col text-center md:text-left">
          <div className="text-2xl font-semibold text-primary mb-3">
            Welcome to your safe space
          </div>

          <div className="text-xl text-muted-foreground">
            How are you feeling today? I&apos;m here to listen and support you.
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ delay: 0.8 }}
        className="hidden md:block mt-8 p-5 rounded-2xl bg-card border border-border shadow-sm"
      >
        <div className="text-sm font-medium text-primary mb-3">
          About Lena AI
        </div>
        <div className="text-sm text-muted-foreground">
          I&apos;m your AI therapy companion, designed to provide a warm,
          judgment-free space for reflection and support. While I&apos;m not a
          replacement for professional therapy, I&apos;m here to listen, offer
          perspective, and help you process your thoughts and feelings.
        </div>
      </motion.div>
    </div>
  );
};
