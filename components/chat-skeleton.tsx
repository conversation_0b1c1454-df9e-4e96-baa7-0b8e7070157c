'use client';

export const ChatSkeleton = () => {
  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      {/* Skeleton header */}
      <div className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 border-b">
        {/* SidebarToggle */}
        <div className="animate-pulse h-9 w-9 bg-muted-foreground/20 rounded-md" />

        {/* ModelSelector */}
        <div className="animate-pulse h-9 w-40 bg-muted-foreground/20 rounded-md" />

        {/* PromptSelector */}
        <div className="animate-pulse h-9 w-40 bg-muted-foreground/20 rounded-md" />

        {/* VisibilitySelector */}
        <div className="animate-pulse h-9 w-28 bg-muted-foreground/20 rounded-md" />
      </div>

      {/* Skeleton messages */}
      <div className="flex-1 flex flex-col py-4 gap-6 overflow-y-auto">
        <div className="w-full mx-auto max-w-3xl px-4 flex flex-col gap-6">
          {/* User message */}
          <div className="flex gap-3">
            <div className="animate-pulse h-8 w-8 flex-shrink-0 bg-muted-foreground/20 rounded-full" />
            <div className="flex flex-col gap-2 w-full">
              <div className="animate-pulse h-24 w-full bg-muted-foreground/20 rounded-2xl" />
            </div>
          </div>

          {/* Assistant message */}
          <div className="flex gap-3">
            <div className="animate-pulse h-8 w-8 flex-shrink-0 bg-muted-foreground/20 rounded-full" />
            <div className="flex flex-col gap-2 w-full">
              <div className="animate-pulse h-32 w-full bg-muted-foreground/20 rounded-2xl" />
            </div>
          </div>

          {/* User message */}
          <div className="flex gap-3">
            <div className="animate-pulse h-8 w-8 flex-shrink-0 bg-muted-foreground/20 rounded-full" />
            <div className="flex flex-col gap-2 w-full">
              <div className="animate-pulse h-16 w-full bg-muted-foreground/20 rounded-2xl" />
            </div>
          </div>
        </div>
      </div>

      {/* Skeleton input */}
      <div className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
        <div className="animate-pulse h-24 w-full bg-muted-foreground/20 rounded-md" />
      </div>
    </div>
  );
};
