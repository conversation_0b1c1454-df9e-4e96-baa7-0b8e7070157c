'use client';

import { useRouter } from 'next/navigation';
import { useWindowSize } from 'usehooks-ts';

import { ModelSelector } from '@/components/model-selector';
import { SidebarToggle } from '@/components/sidebar-toggle';
import { Button } from '@/components/ui/button';
import { PlusIcon, } from './icons';
import { useSidebar } from './ui/sidebar';
import { memo, useEffect, useState } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { type VisibilityType, VisibilitySelector } from './visibility-selector';
import type { Session } from 'next-auth';
import { PromptSelector } from './prompt-selector';
import type { Prompt } from '@/lib/db/schema';
import { usePrompts } from '@/hooks/use-prompts';
import { useDefaultSelections } from '@/hooks/use-default-selections';
import { useAppState } from '@/hooks/use-app-state';

function PureChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  session,
  onModelChange,
  onPromptSelect,
  initialPromptId,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
  onModelChange?: (modelId: string) => void;
  onPromptSelect?: (prompt: Prompt) => void;
  initialPromptId?: string;
}) {
  const router = useRouter();
  const { open } = useSidebar();
  const { width: windowWidth } = useWindowSize();

  const { prompts, loading } = usePrompts();
  const { defaultPrompt, isAdmin } = useDefaultSelections();
  const { setSelectedPrompt } = useAppState();
  const [selectedPromptId, setSelectedPromptId] = useState<
    string | undefined
  >(initialPromptId);

  // Track if prompt has been set
  const [hasSetPrompt, setHasSetPrompt] = useState(false);

  useEffect(() => {
    // Only set the prompt once when data is loaded
    if (!loading && prompts.length > 0 && onPromptSelect && !hasSetPrompt) {
      let promptToSelect = null;

      // Admin users can select different prompts
      if (isAdmin) {
        // If we have an initialPromptId and it exists in the prompts array, use that
        if (initialPromptId && prompts.some(p => p.id === initialPromptId)) {
          promptToSelect = prompts.find(p => p.id === initialPromptId);
        } else {
          // Default to first prompt for admins if no initial prompt
          promptToSelect = prompts[0];
        }
      } else {
        // Non-admin users always use the default prompt
        promptToSelect = defaultPrompt || prompts[0];
      }

      if (promptToSelect) {
        setSelectedPromptId(promptToSelect.id);
        // Update both the local state and the global app state
        onPromptSelect(promptToSelect);
        setSelectedPrompt(promptToSelect);
        setHasSetPrompt(true);
      }
    }
  }, [loading, prompts, onPromptSelect, initialPromptId, defaultPrompt, isAdmin, hasSetPrompt, setSelectedPrompt]);

  const handlePromptSelect = (prompt: Prompt) => {
    setSelectedPromptId(prompt.id);
    // Update both local callback and global state
    if (onPromptSelect) {
      onPromptSelect(prompt);
    }
    setSelectedPrompt(prompt);
  };

  return (
    <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2">
      <SidebarToggle />

      {(!open || windowWidth < 768) && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              className="order-2 md:order-1 md:px-2 px-2 md:h-fit ml-auto md:ml-0"
              onClick={() => {
                router.push('/');
                router.refresh();
              }}
            >
              <PlusIcon />
              <span className="md:sr-only">New Chat</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>New Chat</TooltipContent>
        </Tooltip>
      )}

      {!isReadonly && isAdmin && (
        <ModelSelector
          session={session}
          selectedModelId={selectedModelId}
          onModelChange={onModelChange}
          className="order-1 md:order-2"
        />
      )}
      {isAdmin && (
        <PromptSelector
          onSelect={handlePromptSelect}
          selectedPromptId={selectedPromptId}
          onManagePrompts={() => {
            router.push('/prompts');
          }}
        />
      )}

      {!isReadonly && (
        <VisibilitySelector
          chatId={chatId}
          selectedVisibilityType={selectedVisibilityType}
          className="order-1 md:order-3"
        />
      )}
    </header>
  );
}

export const ChatHeader = memo(PureChatHeader, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.selectedModelId === nextProps.selectedModelId &&
    prevProps.initialPromptId === nextProps.initialPromptId
  );
});
