'use client';

import { Provider } from 'jotai';
import { useEffect } from 'react';
import type { ReactNode } from 'react';
import { useAppState } from '@/hooks/use-app-state';
import {
  getDefaultModelSetting,
  getDefaultPromptSetting,
} from '@/app/(chat)/actions';

/**
 * Initialize the default settings for the application
 */
function DefaultSettingsInitializer() {
  const { setDefaultModelId, setDefaultPrompt } = useAppState();

  useEffect(() => {
    // Load default settings from the server
    const loadDefaults = async () => {
      try {
        const [defaultModelId, defaultPrompt] = await Promise.all([
          getDefaultModelSetting(),
          getDefaultPromptSetting(),
        ]);

        // console.log('Loaded default settings:', defaultModelId, defaultPrompt);

        setDefaultModelId(defaultModelId);
        if (defaultPrompt) {
          setDefaultPrompt(defaultPrompt);
        }
      } catch (error) {
        console.error('Failed to load default settings:', error);
      }
    };

    loadDefaults();
  }, [setDefaultModelId, setDefaultPrompt]);

  return null;
}

/**
 * Provider for the application state using Jotai
 * Ensures all components using the useAppState hook share the same state
 * Also initializes default settings from the server
 */
export function AppStateProvider({ children }: { children: ReactNode }) {
  return (
    <Provider>
      <DefaultSettingsInitializer />
      {children}
    </Provider>
  );
}
