'use client';

import { useState, useEffect, useTransition } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from '@/components/toast';
import { chatModels } from '@/lib/ai/models';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Footer,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { updateDefaultSettings } from '@/app/(chat)/actions';
import { usePrompts } from '@/hooks/use-prompts';
import { useAppState } from '@/hooks/use-app-state';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

export function SettingsModal({ 
  open, 
  onOpenChange 
}: { 
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  const { data: session } = useSession();
  const isAdmin = session?.user?.type === 'admin';
  const { prompts } = usePrompts();
  const { defaultModelId, defaultPrompt } = useAppState();
  
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [selectedPromptId, setSelectedPromptId] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  // Initialize form with current defaults
  useEffect(() => {
    if (defaultModelId) {
      setSelectedModelId(defaultModelId);
    }
    if (defaultPrompt?.id) {
      setSelectedPromptId(defaultPrompt.id);
    }
  }, [defaultModelId, defaultPrompt]);

  if (!isAdmin) {
    return null;
  }

  const handleSave = () => {
    if (!selectedModelId || !selectedPromptId) {
      toast({ 
        type: 'error', 
        description: 'Please select both a model and a prompt.' 
      });
      return;
    }

    startTransition(async () => {
      try {
        await updateDefaultSettings(selectedModelId, selectedPromptId);
        toast({ 
          type: 'success', 
          description: 'Default settings updated successfully!' 
        });
        onOpenChange(false);
      } catch (error) {
        console.error('Failed to update default settings:', error);
        toast({ 
          type: 'error', 
          description: 'Failed to update default settings.' 
        });
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>App Default Settings</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-4 py-4">
          <div className="flex flex-col gap-2">
            <Label htmlFor="default-model">Default Model</Label>
            <Select
              value={selectedModelId || ''}
              onValueChange={setSelectedModelId}
            >
              <SelectTrigger id="default-model">
                <SelectValue placeholder="Select default model" />
              </SelectTrigger>
              <SelectContent>
                {chatModels.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    {model.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="default-prompt">Default Prompt</Label>
            <Select
              value={selectedPromptId || ''}
              onValueChange={setSelectedPromptId}
            >
              <SelectTrigger id="default-prompt">
                <SelectValue placeholder="Select default prompt" />
              </SelectTrigger>
              <SelectContent>
                {prompts && Array.isArray(prompts) ? prompts.map((prompt) => (
                  <SelectItem key={prompt.id} value={prompt.id}>
                    {prompt.name}
                  </SelectItem>
                )) : null}
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={isPending}
          >
            {isPending ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}