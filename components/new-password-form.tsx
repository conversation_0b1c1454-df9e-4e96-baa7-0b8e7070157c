import Form from 'next/form';

import { Input } from './ui/input';
import { Label } from './ui/label';

export function NewPasswordForm({
  action,
  children,
  token,
}: {
  action: NonNullable<
    string | ((formData: FormData) => void | Promise<void>) | undefined
  >;
  children: React.ReactNode;
  token: string;
}) {
  return (
    <Form action={action} className="flex flex-col gap-4 px-4 sm:px-16">
      <input type="hidden" name="token" value={token} />
      
      <div className="flex flex-col gap-2">
        <Label
          htmlFor="password"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          New Password
        </Label>

        <Input
          id="password"
          name="password"
          className="bg-muted text-md md:text-sm"
          type="password"
          required
          autoFocus
          minLength={8}
        />
      </div>

      <div className="flex flex-col gap-2">
        <Label
          htmlFor="confirmPassword"
          className="text-zinc-600 font-normal dark:text-zinc-400"
        >
          Confirm New Password
        </Label>

        <Input
          id="confirmPassword"
          name="confirmPassword"
          className="bg-muted text-md md:text-sm"
          type="password"
          required
          minLength={8}
        />
      </div>

      {children}
    </Form>
  );
}